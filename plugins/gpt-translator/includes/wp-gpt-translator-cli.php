<?php




if (defined('WP_CLI') && WP_CLI) {
    WP_CLI::add_command('gpt_translator:process_chunk', function ($args) {
        $chunk_id = (int) ($args[0] ?? 0);
        $pre_translate_chunk = (bool) ($args[1] ?? false);
        if ($chunk_id > 0) {
            processChunk($chunk_id, $pre_translate_chunk);
            WP_CLI::log(date('Y-m-d H:i:s') . " ✅ Chunk $chunk_id processed.");
        } else {
            WP_CLI::error(date('Y-m-d H:i:s') . "❌ Invalid chunk ID.");
        }
    });

    WP_CLI::add_command('gpt_translator:process_pending', function ($args) {
        global $wpdb;
        $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';
        
        // Find pending chunks
        $pending_chunk = $wpdb->get_row("
            SELECT id FROM $chunk_table 
            WHERE status = 'pending' 
                OR (status = 'processing' AND last_update < NOW() - INTERVAL 3 MINUTE)
            ORDER BY created_at ASC 
            LIMIT 1
        ");
        
        if (!$pending_chunk) {
            WP_CLI::log("No pending chunks found.");
            return;
        }
        
        $chunk_id = (int) $pending_chunk->id;
    
        // Update status using the already obtained chunk_id
        $updated = $wpdb->query(
            $wpdb->prepare(
                "UPDATE $chunk_table 
                SET status = 'processing', last_update = NOW() 
                WHERE id = %d AND (status = 'pending' OR status = 'processing')",
                $chunk_id
            )
        );
    
        if ($updated === false) {
            WP_CLI::error("Failed to update chunk status.");
            return;
        }
        
        WP_CLI::log("Processing chunk ID: $chunk_id");
        processChunk($chunk_id);
        WP_CLI::success("Chunk $chunk_id processed successfully.");
    });
}
